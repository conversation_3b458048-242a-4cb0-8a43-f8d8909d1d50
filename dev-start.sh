#!/bin/bash
# dev-start.sh - 基于uv的开发环境启动脚本

echo "🚀 启动基于uv的Hulu自动化开发环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请启动Docker Desktop"
    exit 1
fi

# 进入项目目录
cd ~/Projects/hulu-automation

# 激活uv虚拟环境
if [ ! -d ".venv" ]; then
    echo "❌ uv虚拟环境不存在，请先运行 ./macos-dev-setup.sh"
    exit 1
fi

echo "⚡ 激活uv虚拟环境..."
source .venv/bin/activate

# 检查依赖是否最新
echo "📦 检查依赖状态..."
if [ ! -f ".deps-installed" ] || [ "pyproject.toml" -nt ".deps-installed" ]; then
    echo "📥 安装/更新依赖..."
    uv pip install -e ".[dev]"
    touch .deps-installed
fi

# 启动Docker服务
echo "🐳 启动Docker服务..."
docker-compose -f docker-compose.dev.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose -f docker-compose.dev.yml ps

# 检查数据库连接
echo "🗄️ 检查数据库连接..."
python -c "
import asyncio
import asyncpg
async def check_db():
    try:
        conn = await asyncpg.connect('postgresql://hulu_user:dev_password@localhost:5432/hulu_dev')
        await conn.close()
        print('✅ 数据库连接正常')
    except Exception as e:
        print(f'❌ 数据库连接失败: {e}')
asyncio.run(check_db())
" 2>/dev/null || echo "⚠️  数据库连接检查跳过"

# 检查Redis连接
echo "🔴 检查Redis连接..."
python -c "
import redis
try:
    r = redis.Redis(host='localhost', port=6379, db=0)
    r.ping()
    print('✅ Redis连接正常')
except Exception as e:
    print(f'❌ Redis连接失败: {e}')
" 2>/dev/null || echo "⚠️  Redis连接检查跳过"

# 运行数据库迁移（如果存在）
if [ -f "backend/alembic.ini" ]; then
    echo "🗄️ 运行数据库迁移..."
    cd backend && alembic upgrade head && cd ..
fi

# 创建测试数据（如果脚本存在）
if [ -f "backend/scripts/create_test_data.py" ]; then
    echo "📝 创建测试数据..."
    python backend/scripts/create_test_data.py
fi

echo "✅ 开发环境启动完成！"
echo ""
echo "🌐 服务访问地址："
echo "- API文档: http://localhost:8000/docs"
echo "- Web界面: http://localhost:3000"
echo "- Grafana: http://localhost:3001 (admin/admin)"
echo "- Prometheus: http://localhost:9090"
echo "- Vault: http://localhost:8200"
echo ""
echo "🛠️ 开发命令："
echo "- 启动API服务: uvicorn backend.app.main:app --reload --host 0.0.0.0 --port 8000"
echo "- 运行测试: pytest backend/tests/"
echo "- 格式化代码: black backend/"
echo "- 类型检查: mypy backend/"
echo "- 停止服务: docker-compose -f docker-compose.dev.yml down"
echo ""
echo "📦 uv命令："
echo "- 安装新包: uv pip install package-name"
echo "- 更新依赖: ./update-deps.sh"
echo "- 检查环境: ./check-env.sh"
