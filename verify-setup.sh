#!/bin/bash
# verify-setup.sh - 验证安装文件完整性

echo "🔍 验证Hulu自动化系统安装文件..."

# 检查必要文件是否存在
files=(
    "macos-dev-setup.sh"
    "init-project.sh"
    "dev-start.sh"
    "docker-compose.dev.yml"
    "README.md"
    ".gitignore"
    "config/prometheus.yml"
)

echo "📋 检查文件完整性："
all_files_exist=true

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (缺失)"
        all_files_exist=false
    fi
done

if [ "$all_files_exist" = true ]; then
    echo ""
    echo "🎉 所有文件检查完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 运行主安装脚本："
    echo "   ./macos-dev-setup.sh"
    echo ""
    echo "2. 初始化项目："
    echo "   ./init-project.sh"
    echo ""
    echo "3. 启动开发环境："
    echo "   ./dev-start.sh"
    echo ""
    echo "4. 访问API文档："
    echo "   http://localhost:8000/docs"
    echo ""
    echo "💡 提示："
    echo "- 确保你有管理员权限"
    echo "- 确保网络连接正常"
    echo "- 安装过程中如遇到问题，请查看README.md"
else
    echo ""
    echo "❌ 部分文件缺失，请重新下载完整的安装包"
fi

# 检查脚本权限
echo ""
echo "🔐 检查脚本执行权限："
for script in *.sh; do
    if [ -x "$script" ]; then
        echo "✅ $script (可执行)"
    else
        echo "⚠️  $script (需要执行权限)"
        chmod +x "$script"
        echo "   已自动添加执行权限"
    fi
done

echo ""
echo "✅ 验证完成！"
