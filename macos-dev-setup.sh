#!/bin/bash
# macos-dev-setup.sh - 基于uv的macOS开发环境配置脚本

echo "🍎 开始配置基于uv的macOS开发环境..."

# 检查系统版本
if [[ $(sw_vers -productVersion | cut -d. -f1) -lt 12 ]]; then
    echo "❌ 需要macOS 12.0或更新版本"
    exit 1
fi

# 1. 安装Homebrew（如果未安装）
if ! command -v brew &> /dev/null; then
    echo "📦 安装Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # 添加到PATH
    if [[ $(uname -m) == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zshrc
        eval "$(/opt/homebrew/bin/brew shellenv)"
    else
        echo 'eval "$(/usr/local/bin/brew shellenv)"' >> ~/.zshrc
        eval "$(/usr/local/bin/brew shellenv)"
    fi
fi

# 2. 更新Homebrew
echo "🔄 更新Homebrew..."
brew update

# 3. 安装基础开发工具（不包括Python，uv会管理Python）
echo "🛠️ 安装基础开发工具..."
brew install \
    git \
    curl \
    wget \
    tree \
    htop \
    jq \
    node \
    postgresql@15 \
    redis

# 4. 安装uv包管理器
echo "⚡ 安装uv包管理器..."
if ! command -v uv &> /dev/null; then
    curl -LsSf https://astral.sh/uv/install.sh | sh
    
    # 添加uv到PATH
    echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.zshrc
    export PATH="$HOME/.cargo/bin:$PATH"
    
    # 重新加载shell配置
    source ~/.zshrc 2>/dev/null || true
fi

# 验证uv安装
if ! command -v uv &> /dev/null; then
    echo "❌ uv安装失败，请手动安装"
    echo "运行: curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

echo "✅ uv版本: $(uv --version)"

# 5. 配置uv加速源（可选，针对中国用户）
echo "🌐 配置uv加速源..."
mkdir -p ~/.config/uv
cat > ~/.config/uv/uv.toml << EOF
[global]
# 使用清华大学镜像源加速下载
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"
extra-index-url = [
    "https://pypi.org/simple",
]

# 启用并行下载
concurrent-downloads = 10

# 缓存配置
cache-dir = "~/.cache/uv"
EOF

# 6. 安装Docker Desktop
echo "🐳 检查Docker Desktop..."
if ! command -v docker &> /dev/null; then
    echo "📥 下载Docker Desktop..."
    if [[ $(uname -m) == "arm64" ]]; then
        # Apple Silicon
        curl -L "https://desktop.docker.com/mac/main/arm64/Docker.dmg" -o ~/Downloads/Docker.dmg
    else
        # Intel
        curl -L "https://desktop.docker.com/mac/main/amd64/Docker.dmg" -o ~/Downloads/Docker.dmg
    fi
    
    echo "📱 请手动安装Docker Desktop："
    echo "1. 打开 ~/Downloads/Docker.dmg"
    echo "2. 将Docker拖拽到Applications文件夹"
    echo "3. 启动Docker Desktop并完成初始化"
    echo "4. 安装完成后按回车继续..."
    read -p ""
else
    echo "✅ Docker Desktop已安装"
fi

# 7. 安装VS Code（可选）
if ! command -v code &> /dev/null; then
    echo "💻 安装VS Code..."
    brew install --cask visual-studio-code
    
    # 安装有用的VS Code扩展
    echo "🔌 安装VS Code扩展..."
    code --install-extension ms-python.python
    code --install-extension ms-python.black-formatter
    code --install-extension ms-python.flake8
    code --install-extension ms-python.mypy-type-checker
    code --install-extension bradlc.vscode-tailwindcss
    code --install-extension esbenp.prettier-vscode
fi

# 8. 创建项目目录
echo "📁 创建项目目录..."
PROJECT_DIR="$HOME/Projects/hulu-automation"
mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# 创建目录结构
mkdir -p {
    backend/{app,tests,migrations,scripts},
    frontend/{src,public},
    docker/{postgres,redis,nginx},
    config/{development,production},
    data/{input,output,temp},
    logs,
    docs,
    .uv
}

# 9. 使用uv初始化Python项目
echo "🐍 使用uv初始化Python项目..."

# 创建pyproject.toml
cat > pyproject.toml << 'EOF'
[project]
name = "hulu-automation"
version = "0.1.0"
description = "Hulu账号支付信息自动化管理系统"
authors = [
    {name = "Developer", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    # Web框架
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",

    # 数据库
    "sqlalchemy>=2.0.23",
    "alembic>=1.12.1",
    "asyncpg>=0.29.0",

    # 缓存和队列
    "redis>=5.0.1",
    "celery>=5.3.4",

    # 浏览器自动化
    "playwright>=1.40.0",

    # 数据处理
    "pandas>=2.1.3",
    "openpyxl>=3.1.2",
    "numpy>=1.25.2",

    # 安全
    "cryptography>=41.0.7",
    "hvac>=2.0.0",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",

    # HTTP客户端
    "aiohttp>=3.9.1",
    "httpx>=0.25.2",

    # 工具库
    "python-multipart>=0.0.6",
    "jinja2>=3.1.2",
    "python-dotenv>=1.0.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
]

[project.optional-dependencies]
dev = [
    # 测试工具
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.2",

    # 代码质量
    "black>=23.11.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "isort>=5.12.0",
    "pre-commit>=3.6.0",

    # 开发工具
    "ipython>=8.17.2",
    "rich>=13.7.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["backend/tests"]
addopts = [
    "--strict-markers",
    "--cov=backend/app",
    "--cov-report=term-missing",
]
EOF

# 10. 使用uv创建虚拟环境并安装依赖
echo "📦 使用uv创建虚拟环境并安装依赖..."

# 创建虚拟环境
uv venv --python 3.11

# 激活虚拟环境并安装依赖
source .venv/bin/activate

# 安装项目依赖（包括开发依赖）
uv pip install -e ".[dev]"

# 11. 安装Playwright浏览器
echo "🎭 安装Playwright浏览器..."
playwright install
playwright install-deps

echo "✅ 基于uv的macOS开发环境配置完成！"
echo ""
echo "📋 下一步操作："
echo "1. cd ~/Projects/hulu-automation"
echo "2. source .venv/bin/activate  # 激活uv虚拟环境"
echo "3. 开始开发工作"
