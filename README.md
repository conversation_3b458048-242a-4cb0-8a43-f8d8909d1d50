# Hulu自动化管理系统

基于uv包管理器的Hulu账号支付信息自动化管理系统。

## 🚀 快速开始

### 环境要求
- macOS 12.0+
- Docker Desktop
- 管理员权限

### 一键安装
```bash
# 下载并运行安装脚本
curl -O https://raw.githubusercontent.com/your-repo/hulu-automation/main/macos-dev-setup.sh
chmod +x macos-dev-setup.sh
./macos-dev-setup.sh
```

### 手动安装步骤
```bash
# 1. 克隆或下载项目文件
# 2. 运行环境配置脚本
chmod +x macos-dev-setup.sh
./macos-dev-setup.sh

# 3. 初始化项目
chmod +x init-project.sh
./init-project.sh

# 4. 启动开发环境
chmod +x dev-start.sh
./dev-start.sh
```

## 🛠️ 开发环境

### 激活开发环境
```bash
# 进入项目目录
cd ~/Projects/hulu-automation

# 激活uv虚拟环境
source .venv/bin/activate

# 或使用便捷脚本
./activate-uv.sh
```

### 启动服务
```bash
# 启动所有Docker服务
./dev-start.sh

# 启动API服务
uvicorn backend.app.main:app --reload --host 0.0.0.0 --port 8000
```

### 开发工具命令
```bash
# 检查环境状态
./check-env.sh

# 更新依赖
./update-deps.sh

# 运行测试
pytest backend/tests/

# 代码格式化
black backend/
isort backend/

# 类型检查
mypy backend/

# 安装新包
uv pip install package-name

# 查看已安装包
uv pip list
```

## 📁 项目结构
```
hulu-automation/
├── backend/              # 后端代码
│   ├── app/             # 应用主代码
│   │   ├── api/         # API路由
│   │   ├── core/        # 核心配置
│   │   ├── models/      # 数据模型
│   │   ├── services/    # 业务逻辑
│   │   └── utils/       # 工具函数
│   ├── tests/           # 测试代码
│   ├── migrations/      # 数据库迁移
│   └── scripts/         # 脚本文件
├── frontend/            # 前端代码
├── docker/              # Docker配置
├── config/              # 配置文件
├── data/                # 数据目录
│   ├── input/           # 输入数据
│   ├── output/          # 输出结果
│   └── temp/            # 临时文件
├── logs/                # 日志文件
├── docs/                # 文档
├── .venv/               # uv虚拟环境
├── pyproject.toml       # 项目配置
├── docker-compose.dev.yml # 开发环境Docker配置
└── README.md            # 项目说明
```

## 🌐 服务访问地址

开发环境启动后，可以访问以下服务：

- **API文档**: http://localhost:8000/docs
- **API接口**: http://localhost:8000
- **Web界面**: http://localhost:3000
- **Grafana监控**: http://localhost:3001 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Vault**: http://localhost:8200

## 🔧 配置说明

### 环境变量
在 `config/development/.env` 中配置：

```bash
# 数据库配置
DATABASE_URL=postgresql://hulu_user:dev_password@localhost:5432/hulu_dev
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=dev-secret-key
VAULT_URL=http://localhost:8200
VAULT_TOKEN=dev_root_token

# 动态IP配置（需要填入真实信息）
DYNAMIC_IP_API_KEY=your-api-key-here
DYNAMIC_IP_PROVIDER_URL=your-provider-url-here

# 应用配置
DEBUG=true
LOG_LEVEL=DEBUG
BROWSER_POOL_SIZE=3
MAX_CONCURRENT_TASKS=10
```

### uv配置
uv配置文件位于 `~/.config/uv/uv.toml`：

```toml
[global]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"
extra-index-url = ["https://pypi.org/simple"]
concurrent-downloads = 10
cache-dir = "~/.cache/uv"
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest backend/tests/test_api.py

# 运行测试并生成覆盖率报告
pytest --cov=backend/app --cov-report=html

# 运行特定标记的测试
pytest -m "not slow"
```

## 📦 依赖管理

### 添加新依赖
```bash
# 添加运行时依赖
uv pip install package-name

# 添加开发依赖
uv pip install --dev package-name

# 从pyproject.toml安装所有依赖
uv pip install -e ".[dev]"
```

### 更新依赖
```bash
# 更新所有依赖
./update-deps.sh

# 或手动更新
uv pip install --upgrade -e ".[dev]"
```

## 🚨 故障排除

### 常见问题

1. **uv命令未找到**
   ```bash
   # 重新安装uv
   curl -LsSf https://astral.sh/uv/install.sh | sh
   source ~/.zshrc
   ```

2. **Docker服务启动失败**
   ```bash
   # 检查Docker Desktop是否运行
   docker info
   
   # 重启Docker服务
   docker-compose -f docker-compose.dev.yml down
   docker-compose -f docker-compose.dev.yml up -d
   ```

3. **数据库连接失败**
   ```bash
   # 检查PostgreSQL容器状态
   docker-compose -f docker-compose.dev.yml ps postgres
   
   # 查看容器日志
   docker-compose -f docker-compose.dev.yml logs postgres
   ```

4. **Playwright浏览器问题**
   ```bash
   # 重新安装浏览器
   playwright install
   playwright install-deps
   ```

### 重置环境
```bash
# 停止所有服务
docker-compose -f docker-compose.dev.yml down -v

# 删除虚拟环境
rm -rf .venv

# 重新运行安装脚本
./macos-dev-setup.sh
```

## 📝 开发指南

### 代码规范
- 使用 Black 进行代码格式化
- 使用 isort 进行导入排序
- 使用 mypy 进行类型检查
- 使用 pytest 编写测试

### 提交前检查
```bash
# 格式化代码
black backend/
isort backend/

# 类型检查
mypy backend/

# 运行测试
pytest

# 检查代码质量
flake8 backend/
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。
