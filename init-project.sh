#!/bin/bash
# init-project.sh - 项目初始化脚本

echo "📦 初始化Hulu自动化项目..."

cd ~/Projects/hulu-automation

# 12. 创建uv专用的脚本
echo "📝 创建uv专用脚本..."

# 创建激活脚本
cat > activate-uv.sh << 'EOF'
#!/bin/bash
# activate-uv.sh - 激活uv虚拟环境

echo "⚡ 激活uv虚拟环境..."
cd ~/Projects/hulu-automation
source .venv/bin/activate
echo "✅ uv虚拟环境已激活"
echo "🐍 Python版本: $(python --version)"
echo "📦 uv版本: $(uv --version)"
EOF

chmod +x activate-uv.sh

# 创建依赖更新脚本
cat > update-deps.sh << 'EOF'
#!/bin/bash
# update-deps.sh - 更新项目依赖

echo "📦 更新项目依赖..."
cd ~/Projects/hulu-automation
source .venv/bin/activate

# 更新所有依赖到最新版本
uv pip install --upgrade -e ".[dev]"

# 重新安装Playwright浏览器（如果有更新）
playwright install

echo "✅ 依赖更新完成"
EOF

chmod +x update-deps.sh

# 创建开发环境检查脚本
cat > check-env.sh << 'EOF'
#!/bin/bash
# check-env.sh - 检查开发环境

echo "🔍 检查开发环境状态..."
cd ~/Projects/hulu-automation

# 检查uv
echo "📦 uv版本: $(uv --version)"

# 检查虚拟环境
if [ -d ".venv" ]; then
    echo "✅ 虚拟环境存在"
    source .venv/bin/activate
    echo "🐍 Python版本: $(python --version)"
    echo "📋 已安装包数量: $(uv pip list | wc -l)"
else
    echo "❌ 虚拟环境不存在"
fi

# 检查Docker
if command -v docker &> /dev/null; then
    if docker info > /dev/null 2>&1; then
        echo "✅ Docker运行正常"
    else
        echo "⚠️  Docker未运行"
    fi
else
    echo "❌ Docker未安装"
fi

# 检查关键依赖
echo "🔍 检查关键依赖..."
python -c "import fastapi; print(f'✅ FastAPI: {fastapi.__version__}')" 2>/dev/null || echo "❌ FastAPI未安装"
python -c "import playwright; print(f'✅ Playwright: {playwright.__version__}')" 2>/dev/null || echo "❌ Playwright未安装"
python -c "import pandas; print(f'✅ Pandas: {pandas.__version__}')" 2>/dev/null || echo "❌ Pandas未安装"
EOF

chmod +x check-env.sh

# 13. 创建空的.gitkeep文件
touch data/input/.gitkeep data/output/.gitkeep data/temp/.gitkeep

# 14. 创建主应用文件
mkdir -p backend/app/{api,core,models,services,utils}

cat > backend/app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(
    title="Hulu自动化管理系统",
    description="Hulu账号支付信息自动化管理API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hulu自动化管理系统API"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

# 创建配置文件
cat > backend/app/core/config.py << 'EOF'
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # 数据库配置
    database_url: str = "postgresql://hulu_user:dev_password@localhost:5432/hulu_dev"
    redis_url: str = "redis://localhost:6379/0"
    
    # 安全配置
    secret_key: str = "dev-secret-key"
    vault_url: str = "http://localhost:8200"
    vault_token: str = "dev_root_token"
    
    # 动态IP配置
    dynamic_ip_api_key: Optional[str] = None
    dynamic_ip_provider_url: Optional[str] = None
    
    # 应用配置
    debug: bool = True
    log_level: str = "DEBUG"
    browser_pool_size: int = 3
    max_concurrent_tasks: int = 10
    
    class Config:
        env_file = "config/development/.env"

settings = Settings()
EOF

echo "✅ 项目初始化完成！"
