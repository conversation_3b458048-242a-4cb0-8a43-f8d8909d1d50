version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: hulu-postgres-dev
    environment:
      POSTGRES_DB: hulu_dev
      POSTGRES_USER: hulu_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - hulu-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hulu_user -d hulu_dev"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: hulu-redis-dev
    command: redis-server --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - hulu-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # InfluxDB时序数据库
  influxdb:
    image: influxdb:2.7-alpine
    container_name: hulu-influxdb-dev
    environment:
      DOCKER_INFLUXDB_INIT_MODE: setup
      DOCKER_INFLUXDB_INIT_USERNAME: admin
      DOCKER_INFLUXDB_INIT_PASSWORD: admin_password
      DOCKER_INFLUXDB_INIT_ORG: hulu-org
      DOCKER_INFLUXDB_INIT_BUCKET: metrics
    ports:
      - "8086:8086"
    volumes:
      - influxdb_dev_data:/var/lib/influxdb2
    networks:
      - hulu-dev-network

  # HashiCorp Vault
  vault:
    image: vault:latest
    container_name: hulu-vault-dev
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: dev_root_token
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    ports:
      - "8200:8200"
    cap_add:
      - IPC_LOCK
    networks:
      - hulu-dev-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: hulu-prometheus-dev
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_dev_data:/prometheus
    networks:
      - hulu-dev-network

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: hulu-grafana-dev
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    ports:
      - "3001:3000"
    volumes:
      - grafana_dev_data:/var/lib/grafana
    networks:
      - hulu-dev-network

volumes:
  postgres_dev_data:
  redis_dev_data:
  influxdb_dev_data:
  prometheus_dev_data:
  grafana_dev_data:

networks:
  hulu-dev-network:
    driver: bridge
